<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест админских эндпоинтов</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .users-table th, .users-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .users-table th {
            background-color: #f2f2f2;
        }
        .status-active {
            color: green;
            font-weight: bold;
        }
        .status-blocked {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Тест админских эндпоинтов</h1>
    
    <div class="section">
        <h2>Настройки</h2>
        <div class="form-group">
            <label for="adminToken">Токен администратора:</label>
            <input type="text" id="adminToken" placeholder="Введите токен администратора">
        </div>
    </div>

    <div class="section">
        <h2>1. Получение списка пользователей</h2>
        <div class="form-group">
            <label for="page">Страница:</label>
            <input type="number" id="page" value="1" min="1">
        </div>
        <div class="form-group">
            <label for="limit">Лимит:</label>
            <input type="number" id="limit" value="20" min="1" max="100">
        </div>
        <div class="form-group">
            <label for="search">Поиск:</label>
            <input type="text" id="search" placeholder="Имя, фамилия или email">
        </div>
        <div class="form-group">
            <label for="status">Статус:</label>
            <select id="status">
                <option value="">Все</option>
                <option value="active">Активные</option>
                <option value="blocked">Заблокированные</option>
            </select>
        </div>
        <button onclick="getUsers()">Получить пользователей</button>
        <div id="usersResult" class="result" style="display: none;"></div>
    </div>

    <div class="section">
        <h2>2. Детальная информация о пользователе</h2>
        <div class="form-group">
            <label for="userIdDetails">ID пользователя:</label>
            <input type="number" id="userIdDetails" placeholder="1" min="1">
        </div>
        <button onclick="getUserDetails()">Получить детали</button>
        <div id="userDetailsResult" class="result" style="display: none;"></div>
    </div>

    <div class="section">
        <h2>3. Изменение статуса пользователя</h2>
        <div class="form-group">
            <label for="userIdStatus">ID пользователя:</label>
            <input type="number" id="userIdStatus" placeholder="1" min="1">
        </div>
        <div class="form-group">
            <label for="newStatus">Новый статус:</label>
            <select id="newStatus">
                <option value="active">Активный</option>
                <option value="blocked">Заблокированный</option>
            </select>
        </div>
        <div class="form-group">
            <label for="reason">Причина:</label>
            <textarea id="reason" placeholder="Причина изменения статуса" rows="3"></textarea>
        </div>
        <button onclick="updateUserStatus()">Изменить статус</button>
        <div id="statusResult" class="result" style="display: none;"></div>
    </div>

    <div class="section">
        <h2>4. Прогресс пользователя</h2>
        <div class="form-group">
            <label for="userIdProgress">ID пользователя:</label>
            <input type="number" id="userIdProgress" placeholder="1" min="1">
        </div>
        <button onclick="getUserProgress()">Получить прогресс</button>
        <div id="progressResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000/v1';

        function getAuthHeaders() {
            const token = document.getElementById('adminToken').value.trim();
            if (!token) {
                alert('Введите токен администратора');
                return null;
            }
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }

        function showResult(elementId, data, isError = false) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.style.display = 'block';
        }

        async function getUsers() {
            const headers = getAuthHeaders();
            if (!headers) return;

            const params = new URLSearchParams();
            const page = document.getElementById('page').value;
            const limit = document.getElementById('limit').value;
            const search = document.getElementById('search').value.trim();
            const status = document.getElementById('status').value;

            if (page) params.append('page', page);
            if (limit) params.append('limit', limit);
            if (search) params.append('search', search);
            if (status) params.append('status', status);

            try {
                const response = await fetch(`${API_BASE}/admin/users?${params}`, {
                    method: 'GET',
                    headers: headers
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('usersResult', data);
                } else {
                    showResult('usersResult', `Ошибка: ${response.status} - ${data.error || 'Unknown error'}`, true);
                }
            } catch (error) {
                showResult('usersResult', `Ошибка: ${error.message}`, true);
            }
        }

        async function getUserDetails() {
            const headers = getAuthHeaders();
            if (!headers) return;

            const userId = document.getElementById('userIdDetails').value;
            if (!userId) {
                alert('Введите ID пользователя');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/admin/users/${userId}`, {
                    method: 'GET',
                    headers: headers
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('userDetailsResult', data);
                } else {
                    showResult('userDetailsResult', `Ошибка: ${response.status} - ${data.error || 'Unknown error'}`, true);
                }
            } catch (error) {
                showResult('userDetailsResult', `Ошибка: ${error.message}`, true);
            }
        }

        async function updateUserStatus() {
            const headers = getAuthHeaders();
            if (!headers) return;

            const userId = document.getElementById('userIdStatus').value;
            const status = document.getElementById('newStatus').value;
            const reason = document.getElementById('reason').value.trim();

            if (!userId) {
                alert('Введите ID пользователя');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/admin/users/${userId}/status`, {
                    method: 'PUT',
                    headers: headers,
                    body: JSON.stringify({
                        status: status,
                        reason: reason
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('statusResult', data);
                } else {
                    showResult('statusResult', `Ошибка: ${response.status} - ${data.error || 'Unknown error'}`, true);
                }
            } catch (error) {
                showResult('statusResult', `Ошибка: ${error.message}`, true);
            }
        }

        async function getUserProgress() {
            const headers = getAuthHeaders();
            if (!headers) return;

            const userId = document.getElementById('userIdProgress').value;
            if (!userId) {
                alert('Введите ID пользователя');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/admin/users/${userId}/progress`, {
                    method: 'GET',
                    headers: headers
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('progressResult', data);
                } else {
                    showResult('progressResult', `Ошибка: ${response.status} - ${data.error || 'Unknown error'}`, true);
                }
            } catch (error) {
                showResult('progressResult', `Ошибка: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>
