package data

// Дополнительные утилиты для работы с ролями

// ValidRoles возвращает список всех допустимых ролей
func ValidRoles() []string {
	return []string{RoleAdmin, RoleUser}
}

// IsValidRole проверяет, является ли роль допустимой
func IsValidRole(role string) bool {
	for _, validRole := range ValidRoles() {
		if role == validRole {
			return true
		}
	}
	return false
}

// GetDefaultRole возвращает роль по умолчанию для новых пользователей
func GetDefaultRole() string {
	return RoleUser
}

// RolePermissions определяет разрешения для каждой роли
type RolePermissions struct {
	CanManageUsers    bool
	CanViewAllUsers   bool
	CanModifyContent  bool
	CanViewAnalytics  bool
	CanAccessAdmin    bool
}

// GetRolePermissions возвращает разрешения для указанной роли
func GetRolePermissions(role string) RolePermissions {
	switch role {
	case RoleAdmin:
		return RolePermissions{
			CanManageUsers:    true,
			CanViewAllUsers:   true,
			CanModifyContent:  true,
			CanViewAnalytics:  true,
			CanAccessAdmin:    true,
		}
	case RoleUser:
		return RolePermissions{
			CanManageUsers:    false,
			CanViewAllUsers:   false,
			CanModifyContent:  false,
			CanViewAnalytics:  false,
			CanAccessAdmin:    false,
		}
	default:
		// Для неизвестных ролей возвращаем минимальные права
		return RolePermissions{}
	}
}

// HasPermission проверяет, имеет ли пользователь определенное разрешение
func (u *User) HasPermission(permission string) bool {
	rolePerms := GetRolePermissions(u.Role)
	
	switch permission {
	case "manage_users":
		return rolePerms.CanManageUsers
	case "view_all_users":
		return rolePerms.CanViewAllUsers
	case "modify_content":
		return rolePerms.CanModifyContent
	case "view_analytics":
		return rolePerms.CanViewAnalytics
	case "access_admin":
		return rolePerms.CanAccessAdmin
	default:
		return false
	}
}

// CanAccessAdminPanel проверяет, может ли пользователь получить доступ к админ-панели
func (u *User) CanAccessAdminPanel() bool {
	return u.HasPermission("access_admin")
}

// CanManageUsers проверяет, может ли пользователь управлять другими пользователями
func (u *User) CanManageUsers() bool {
	return u.HasPermission("manage_users")
}
