-- Up
-- До<PERSON><PERSON>вляем поле role в таблицу users
ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT 'user' NOT NULL;

-- Создаем ограничение для допустимых значений ролей
ALTER TABLE users ADD CONSTRAINT check_user_role CHECK (role IN ('admin', 'user'));

-- Создаем индекс для быстрого поиска по ролям
CREATE INDEX idx_users_role ON users(role);

-- Устанавливаем роль admin для существующего админа (если есть)
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';
