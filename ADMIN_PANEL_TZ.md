# Техническое задание: Админ панель для Kazakh-Lingo

## 📋 Общее описание

Необходимо создать веб-интерфейс администратора для управления образовательной платформой Kazakh-Lingo. Админ панель должна предоставлять полный контроль над всеми аспектами системы: пользователями, контентом, статистикой и настройками.

## 🎯 Цели и задачи

### Основные цели:
1. **Управление контентом** - создание, редактирование и удаление образовательных материалов
2. **Управление пользователями** - мониторинг активности, модерация, поддержка
3. **Аналитика и отчеты** - визуализация данных о прогрессе обучения
4. **Системное администрирование** - настройки, логи, мониторинг

### Целевая аудитория:
- Администраторы системы
- Контент-менеджеры
- Преподаватели казахского языка
- Аналитики данных

## 🏗️ Архитектура решения

### Технический стек:
- **Frontend**: React.js + TypeScript
- **State Management**: Redux Toolkit или Zustand
- **HTTP Client**: Axios
- **Charts**: Chart.js или Recharts
- **Authentication**: JWT токены
- **Routing**: React Router

### Структура проекта:
```
admin-panel/
├── src/
│   ├── components/          # Переиспользуемые компоненты
│   ├── pages/              # Страницы админ панели
│   ├── services/           # API сервисы
│   ├── hooks/              # Custom hooks
│   ├── utils/              # Утилиты
│   ├── types/              # TypeScript типы
│   └── constants/          # Константы
├── public/
└── package.json
```

## 📱 Функциональные требования

### 1. Аутентификация и авторизация

#### 1.1 Страница входа
- **Назначение**: Безопасный вход администраторов в систему
- **Поля**: Email, пароль, "Запомнить меня"
- **Валидация**: Проверка формата email, минимальная длина пароля
- **Обработка ошибок**: Показ сообщений об ошибках входа

#### 1.2 Управление сессией
- **JWT токены**: Автоматическое обновление access токенов
- **Автовыход**: При истечении сессии или неактивности
- **Роли**: Разграничение доступа по ролям (super-admin, content-manager, analyst)

### 2. Dashboard (Главная страница)

#### 2.1 Ключевые метрики
- **Общая статистика**:
  - Количество активных пользователей
  - Количество модулей и уроков
  - Средний прогресс обучения
  - Количество загруженных файлов

#### 2.2 Графики и диаграммы
- **Регистрации пользователей** по дням/неделям/месяцам
- **Активность обучения** - количество завершенных модулей
- **Популярные модули** - топ изучаемых модулей
- **География пользователей** (если доступно)

#### 2.3 Быстрые действия
- Кнопки для создания нового контента
- Ссылки на последние добавленные материалы
- Уведомления о системных событиях

### 3. Управление пользователями

#### 3.1 Список пользователей
- **Таблица пользователей** с колонками:
  - ID, Имя, Фамилия, Email
  - Дата регистрации, Последняя активность
  - Статус (активен/заблокирован)
  - Прогресс обучения (%)
  - Действия (просмотр, редактирование, блокировка)

#### 3.2 Фильтрация и поиск
- **Поиск** по имени, фамилии, email
- **Фильтры**: по дате регистрации, статусу, уровню прогресса
- **Сортировка** по любой колонке
- **Пагинация** (по 20/50/100 записей на страницу)

#### 3.3 Детальная информация о пользователе
- **Профиль**: Полная информация о пользователе
- **Прогресс**: Детальная статистика обучения
- **Достижения**: Полученные награды и бейджи
- **История активности**: Логи действий пользователя
- **Действия**: Редактирование, блокировка, сброс пароля

### 4. Управление контентом

#### 4.1 Управление словами (Words)
- **Список слов**: Таблица с казахскими и русскими переводами
- **Добавление слова**: Форма с полями kaz_plaintext, rus_plaintext, audio_url
- **Редактирование**: Изменение переводов и аудио файлов
- **Удаление**: С подтверждением и проверкой использования
- **Импорт**: Массовая загрузка из CSV/Excel файлов

#### 4.2 Управление предложениями (Sentences)
- **Список предложений**: Примеры с переводами
- **Создание**: Форма для добавления новых примеров
- **Связи**: Показ использования в теориях
- **Аудио**: Управление аудио файлами произношения

#### 4.3 Управление вопросами (Questions)
- **Конструктор вопросов**: Интерфейс для создания заданий
- **Типы вопросов**: Перевод, аудирование, составление предложений
- **Привязка слов**: Выбор слов для вопроса с указанием порядка
- **Правильные ответы**: Настройка корректных вариантов
- **Предварительный просмотр**: Как вопрос будет выглядеть для пользователя

#### 4.4 Управление теориями (Theories)
- **Редактор теорий**: Rich text editor для создания контента
- **Структура**: Заголовок, описание, теги, примеры
- **Привязка примеров**: Выбор предложений для демонстрации
- **Предварительный просмотр**: Как теория отображается пользователю

#### 4.5 Управление модулями (Modules)
- **Конструктор модулей**: Drag & drop интерфейс для создания
- **Структура обучения**: Последовательность теорий и вопросов
- **Предварительные требования**: Настройка зависимостей между модулями
- **Уровни сложности**: Классификация по сложности
- **Тестирование**: Возможность пройти модуль как пользователь

### 5. Управление файлами

#### 5.1 Файловый менеджер
- **Браузер файлов**: Просмотр содержимого MinIO buckets
- **Загрузка**: Drag & drop интерфейс для загрузки файлов
- **Организация**: Создание папок и перемещение файлов
- **Предварительный просмотр**: Для изображений и аудио файлов
- **Метаданные**: Размер, дата создания, тип файла

#### 5.2 Аудио файлы
- **Плеер**: Встроенный аудио плеер для прослушивания
- **Качество**: Информация о битрейте и формате
- **Привязки**: Показ где используется аудио файл
- **Массовые операции**: Загрузка и обработка множества файлов

#### 5.3 Изображения
- **Галерея**: Превью изображений в виде сетки
- **Редактор**: Базовое редактирование (обрезка, изменение размера)
- **Оптимизация**: Сжатие изображений для веба
- **Alt-теги**: Управление альтернативным текстом

### 6. Аналитика и отчеты

#### 6.1 Статистика пользователей
- **Активность**: Графики входов и активности по времени
- **Прогресс**: Распределение пользователей по уровням прогресса
- **Ретенция**: Показатели удержания пользователей
- **Сегментация**: Анализ по демографическим данным

#### 6.2 Статистика контента
- **Популярность модулей**: Какие модули изучают чаще всего
- **Сложность**: Анализ ошибок в вопросах
- **Эффективность**: Время прохождения модулей
- **Обратная связь**: Рейтинги и комментарии (если есть)

#### 6.3 Системная аналитика
- **Производительность**: Время ответа API, нагрузка на сервер
- **Ошибки**: Логи ошибок и их частота
- **Использование ресурсов**: Место на диске, трафик
- **Безопасность**: Попытки несанкционированного доступа

### 7. Система достижений

#### 7.1 Управление достижениями
- **Список достижений**: Все доступные награды
- **Создание**: Конструктор новых достижений
- **Типы**: Различные категории (прогресс, активность, особые)
- **Условия**: Настройка условий получения
- **Иконки**: Управление визуальным оформлением

#### 7.2 Мониторинг прогресса
- **Статистика получения**: Сколько пользователей получили каждое достижение
- **Редкие достижения**: Выявление слишком сложных или легких наград
- **Мотивация**: Анализ влияния достижений на активность

### 8. Системное администрирование

#### 8.1 Настройки системы
- **Конфигурация**: Основные параметры приложения
- **Лимиты**: Rate limiting, размеры файлов
- **Уведомления**: Настройка email и push уведомлений
- **Интеграции**: Настройка внешних сервисов

#### 8.2 Мониторинг системы
- **Здоровье сервисов**: Статус всех компонентов системы
- **Логи**: Просмотр и фильтрация системных логов
- **Метрики**: Графики производительности
- **Алерты**: Настройка уведомлений о проблемах

#### 8.3 Резервное копирование
- **Бэкапы**: Управление резервными копиями
- **Восстановление**: Процедуры восстановления данных
- **Миграции**: Управление миграциями базы данных
- **Экспорт данных**: Выгрузка данных в различных форматах

## 🎨 UI/UX требования

### Дизайн принципы:
1. **Минимализм**: Чистый и понятный интерфейс
2. **Консистентность**: Единообразие элементов
3. **Отзывчивость**: Адаптация под разные экраны
4. **Доступность**: Соответствие WCAG 2.1 стандартам

### Цветовая схема:
- **Основной цвет**: #1976d2 (синий)
- **Вторичный цвет**: #dc004e (красный для предупреждений)
- **Успех**: #388e3c (зеленый)
- **Предупреждение**: #f57c00 (оранжевый)
- **Фон**: #f5f5f5 (светло-серый)

### Типографика:
- **Основной шрифт**: Roboto или системный шрифт
- **Заголовки**: 24px, 20px, 18px, 16px
- **Основной текст**: 14px
- **Мелкий текст**: 12px

### Компоненты:
- **Кнопки**: Material Design стиль с hover эффектами
- **Формы**: Четкие лейблы, валидация в реальном времени
- **Таблицы**: Сортировка, фильтрация, пагинация
- **Модальные окна**: Для подтверждения действий
- **Уведомления**: Toast сообщения для обратной связи

## 📊 Технические требования

### Производительность:
- **Время загрузки**: Первая страница < 3 сек
- **Отзывчивость**: Реакция на действия < 200ms
- **Пагинация**: Для списков > 100 элементов
- **Кэширование**: Кэширование статических данных

### Безопасность:
- **Аутентификация**: JWT токены с refresh механизмом
- **Авторизация**: Проверка прав доступа на каждое действие
- **Валидация**: Клиентская и серверная валидация
- **HTTPS**: Обязательное использование HTTPS
- **CSP**: Content Security Policy заголовки

### Совместимость:
- **Браузеры**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Разрешения**: 1920x1080, 1366x768, планшеты, мобильные
- **Доступность**: Поддержка скрин-ридеров, навигация с клавиатуры

### Мониторинг:
- **Логирование**: Все действия пользователей
- **Ошибки**: Автоматическая отправка ошибок
- **Аналитика**: Использование функций админ панели
- **Производительность**: Мониторинг времени загрузки

## 🔌 API Integration - Примеры запросов

### Базовая конфигурация

```typescript
// API Configuration
const API_BASE_URL = 'http://localhost:8080/v1';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor для добавления токена
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### 1. Аутентификация

#### 1.1 Вход администратора
```typescript
// POST /v1/auth/login
const loginAdmin = async (credentials: LoginCredentials) => {
  const response = await apiClient.post('/auth/login', {
    email: credentials.email,
    password: credentials.password
  });

  // Сохранение токенов
  localStorage.setItem('accessToken', response.data.tokens.access_token);
  localStorage.setItem('refreshToken', response.data.tokens.refresh_token);

  return response.data;
};

// Пример использования
const handleLogin = async () => {
  try {
    const result = await loginAdmin({
      email: '<EMAIL>',
      password: 'admin123456'
    });
    console.log('Успешный вход:', result.user);
  } catch (error) {
    console.error('Ошибка входа:', error.response.data.error);
  }
};
```

**💡 Смысл метода**: Безопасная аутентификация администратора с получением JWT токенов для доступа к защищенным ресурсам системы.

#### 1.2 Проверка токена
```typescript
// GET /v1/auth/verify (предполагаемый эндпоинт)
const verifyToken = async () => {
  const response = await apiClient.get('/auth/verify');
  return response.data;
};
```

**💡 Смысл метода**: Проверка валидности текущего токена для поддержания сессии пользователя.

### 2. Управление пользователями

#### 2.1 Получение списка пользователей
```typescript
// GET /v1/admin/users (реализовано)
interface UserFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'active' | 'blocked';
  dateFrom?: string;
  dateTo?: string;
}

const getUsers = async (filters: UserFilters = {}) => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value) params.append(key, value.toString());
  });

  const response = await apiClient.get(`/admin/users?${params}`);
  return response.data;
};

// Пример использования
const loadUsers = async () => {
  const users = await getUsers({
    page: 1,
    limit: 20,
    search: 'айдар',
    status: 'active'
  });
  console.log('Пользователи:', users.users);
  console.log('Всего:', users.total);
};
```

**💡 Смысл метода**: Получение списка всех пользователей системы с возможностью фильтрации и поиска для эффективного управления пользовательской базой.

#### 2.2 Получение детальной информации о пользователе
```typescript
// GET /v1/admin/users/{id} (реализовано)
const getUserDetails = async (userId: number) => {
  const response = await apiClient.get(`/admin/users/${userId}`);
  return response.data;
};

// Пример ответа
const userDetails = {
  user: {
    id: 1,
    name: "Айдар",
    surname: "Нурланов",
    email: "<EMAIL>",
    created_at: "2024-01-01T12:00:00Z",
    last_activity: "2024-01-15T10:30:00Z",
    status: "active"
  },
  progress: {
    completed_modules: 5,
    total_modules: 20,
    completion_percentage: 25.0,
    total_time_spent: "02:45:30",
    current_streak: 7
  },
  achievements: [
    {
      id: 1,
      name: "Первые шаги",
      achieved_at: "2024-01-02T14:20:00Z"
    }
  ]
};
```

**💡 Смысл метода**: Получение полной информации о конкретном пользователе включая прогресс обучения и достижения для детального анализа и поддержки.

#### 2.3 Блокировка/разблокировка пользователя
```typescript
// PUT /v1/admin/users/{id}/status (реализовано)
const updateUserStatus = async (userId: number, status: 'active' | 'blocked', reason?: string) => {
  const response = await apiClient.put(`/admin/users/${userId}/status`, {
    status,
    reason
  });
  return response.data;
};

// Пример использования
const blockUser = async (userId: number) => {
  await updateUserStatus(userId, 'blocked', 'Нарушение правил использования');
  console.log('Пользователь заблокирован');
};
```

**💡 Смысл метода**: Управление статусом пользователя для модерации и обеспечения безопасности платформы.

### 3. Управление словами

#### 3.1 Получение списка слов
```typescript
// GET /v1/word
const getWords = async () => {
  const response = await apiClient.get('/word');
  return response.data.words;
};

// Пример использования
const loadWords = async () => {
  const words = await getWords();
  console.log('Словарь:', words);
};
```

**💡 Смысл метода**: Получение полного словаря для управления базовыми единицами обучения - словами с переводами.

#### 3.2 Создание нового слова
```typescript
// POST /v1/word
interface CreateWordData {
  kaz_plaintext: string;
  rus_plaintext: string;
  audio_url?: string;
}

const createWord = async (wordData: CreateWordData) => {
  const response = await apiClient.post('/word', wordData);
  return response.data.word;
};

// Пример использования
const addNewWord = async () => {
  const newWord = await createWord({
    kaz_plaintext: "сәлем",
    rus_plaintext: "привет",
    audio_url: "http://minio:9000/klingo-audio/salem.mp3"
  });
  console.log('Создано слово:', newWord);
};

// Создание слова с загрузкой аудио файла
const createWordWithAudio = async (wordData: { kaz_plaintext: string; rus_plaintext: string }, audioFile?: File) => {
  const formData = new FormData();
  formData.append('kaz_plaintext', wordData.kaz_plaintext);
  formData.append('rus_plaintext', wordData.rus_plaintext);

  if (audioFile) {
    formData.append('audio', audioFile);
  }

  const response = await apiClient.post('/word/with-audio', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data.word;
};

// Пример использования с файлом
const addWordWithAudioFile = async (audioFile: File) => {
  const newWord = await createWordWithAudio({
    kaz_plaintext: "сәлем",
    rus_plaintext: "привет"
  }, audioFile);
  console.log('Создано слово с аудио:', newWord);
};

// Пример использования без файла
const addWordWithoutAudio = async () => {
  const newWord = await createWordWithAudio({
    kaz_plaintext: "кітап",
    rus_plaintext: "книга"
  });
  console.log('Создано слово без аудио:', newWord);
};
```

**💡 Смысл метода**: Добавление новых слов в словарь для расширения образовательного контента и создания новых заданий. Новый метод `POST /v1/word/with-audio` позволяет загружать аудио файл вместе с созданием слова в одном запросе.

#### 3.3 Обновление слова
```typescript
// PUT /v1/word/{id} (требует реализации)
const updateWord = async (wordId: number, wordData: Partial<CreateWordData>) => {
  const response = await apiClient.put(`/word/${wordId}`, wordData);
  return response.data.word;
};
```

**💡 Смысл метода**: Редактирование существующих слов для исправления ошибок или улучшения переводов.

#### 3.4 Удаление слова
```typescript
// DELETE /v1/word/{id} (требует реализации)
const deleteWord = async (wordId: number) => {
  const response = await apiClient.delete(`/word/${wordId}`);
  return response.data;
};
```

**💡 Смысл метода**: Удаление неактуальных или ошибочных слов из словаря с проверкой их использования в вопросах.

### 4. Управление предложениями

#### 4.1 Получение списка предложений
```typescript
// GET /v1/sentence/all
const getSentences = async () => {
  const response = await apiClient.get('/sentence/all');
  return response.data.sentences;
};
```

**💡 Смысл метода**: Получение всех примеров предложений для управления контекстными примерами в теориях.

#### 4.2 Создание предложения
```typescript
// POST /v1/sentence
interface CreateSentenceData {
  kaz_plaintext: string;
  rus_plaintext: string;
  audio_url?: string;
}

const createSentence = async (sentenceData: CreateSentenceData) => {
  const response = await apiClient.post('/sentence', sentenceData);
  return response.data.sentence;
};

// Пример использования
const addSentence = async () => {
  const sentence = await createSentence({
    kaz_plaintext: "Сәлем, қалың қалай?",
    rus_plaintext: "Привет, как дела?",
    audio_url: "http://minio:9000/klingo-audio/greeting.mp3"
  });
  console.log('Создано предложение:', sentence);
};

// Создание предложения с загрузкой аудио файла
const createSentenceWithAudio = async (sentenceData: { kaz_plaintext: string; rus_plaintext: string }, audioFile?: File) => {
  const formData = new FormData();
  formData.append('kaz_plaintext', sentenceData.kaz_plaintext);
  formData.append('rus_plaintext', sentenceData.rus_plaintext);

  if (audioFile) {
    formData.append('audio', audioFile);
  }

  const response = await apiClient.post('/sentence/with-audio', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data.sentence;
};

// Пример использования с файлом
const addSentenceWithAudioFile = async (audioFile: File) => {
  const sentence = await createSentenceWithAudio({
    kaz_plaintext: "Сәлем, қалың қалай?",
    rus_plaintext: "Привет, как дела?"
  }, audioFile);
  console.log('Создано предложение с аудио:', sentence);
};
```

**💡 Смысл метода**: Добавление новых примеров предложений для демонстрации использования слов в контексте. Новый метод `POST /v1/sentence/with-audio` позволяет загружать аудио файл вместе с созданием предложения в одном запросе.

#### 4.3 Получение конкретного предложения
```typescript
// GET /v1/sentence?id={id}
const getSentence = async (sentenceId: number) => {
  const response = await apiClient.get(`/sentence?id=${sentenceId}`);
  return response.data.sentence;
};
```

**💡 Смысл метода**: Получение детальной информации о конкретном предложении для редактирования или просмотра.

### 5. Управление вопросами

#### 5.1 Получение списка вопросов
```typescript
// GET /v1/questions/all
const getQuestions = async () => {
  const response = await apiClient.get('/questions/all');
  return response.data.questions;
};
```

**💡 Смысл метода**: Получение всех интерактивных заданий для управления системой проверки знаний.

#### 5.2 Создание вопроса
```typescript
// POST /v1/questions
interface CreateQuestionData {
  type: string;
  words: Array<{
    id: number;
    sequence_order: number;
  }>;
  correct_answer: string;
  image_url?: string;
}

const createQuestion = async (questionData: CreateQuestionData) => {
  const response = await apiClient.post('/questions', questionData);
  return response.data.question;
};

// Пример создания вопроса на перевод
const addTranslationQuestion = async () => {
  const question = await createQuestion({
    type: "translation",
    words: [
      { id: 1, sequence_order: 1 }, // слово "сәлем"
      { id: 2, sequence_order: 2 }  // слово "достар"
    ],
    correct_answer: "привет друзья",
    image_url: "http://minio:9000/klingo-images/greeting.jpg"
  });
  console.log('Создан вопрос:', question);
};
```

**💡 Смысл метода**: Создание новых интерактивных заданий для проверки знаний пользователей с привязкой к словам.

#### 5.3 Получение конкретного вопроса
```typescript
// GET /v1/questions?id={id}
const getQuestion = async (questionId: number) => {
  const response = await apiClient.get(`/questions?id=${questionId}`);
  return response.data.question;
};
```

**💡 Смысл метода**: Получение полной информации о вопросе включая связанные слова для редактирования или анализа.

### 6. Управление теориями

#### 6.1 Получение списка теорий
```typescript
// GET /v1/theory/all
const getTheories = async () => {
  const response = await apiClient.get('/theory/all');
  return response.data.theories;
};
```

**💡 Смысл метода**: Получение всех теоретических материалов для управления образовательным контентом.

#### 6.2 Создание теории
```typescript
// POST /v1/theory
interface CreateTheoryData {
  title: string;
  description: string;
  module_id: number;
  tags: string[];
  example_ids: number[];
}

const createTheory = async (theoryData: CreateTheoryData) => {
  const response = await apiClient.post('/theory', theoryData);
  return response.data.theory;
};

// Пример создания теории
const addTheory = async () => {
  const theory = await createTheory({
    title: "Основы приветствия",
    description: "В этом уроке мы изучим основные фразы приветствия на казахском языке...",
    module_id: 1,
    tags: ["приветствие", "базовый", "общение"],
    example_ids: [1, 2, 3] // ID предложений-примеров
  });
  console.log('Создана теория:', theory);
};
```

**💡 Смысл метода**: Создание теоретических материалов с примерами для объяснения правил и особенностей языка.

#### 6.3 Обновление теории
```typescript
// PUT /v1/theory
const updateTheory = async (theoryId: number, theoryData: Partial<CreateTheoryData>) => {
  const response = await apiClient.put('/theory', {
    id: theoryId,
    ...theoryData
  });
  return response.data.theory;
};
```

**💡 Смысл метода**: Редактирование существующих теоретических материалов для улучшения качества обучения.

#### 6.4 Удаление теории
```typescript
// DELETE /v1/theory?id={id}
const deleteTheory = async (theoryId: number) => {
  const response = await apiClient.delete(`/theory?id=${theoryId}`);
  return response.data;
};
```

**💡 Смысл метода**: Удаление устаревших или неактуальных теоретических материалов.

### 7. Управление модулями

#### 7.1 Получение списка модулей
```typescript
// GET /v1/module/all
const getModules = async () => {
  const response = await apiClient.get('/module/all');
  return response.data.modules;
};
```

**💡 Смысл метода**: Получение всех учебных модулей для управления структурой курса.

#### 7.2 Получение полного модуля
```typescript
// GET /v1/module?id={id}
const getFullModule = async (moduleId: number) => {
  const response = await apiClient.get(`/module?id=${moduleId}`);
  return response.data.module;
};

// Пример ответа с полным контентом
const fullModuleExample = {
  id: 1,
  name: "Базовые приветствия",
  theory_ids: [1, 2],
  question_ids: [1, 2, 3],
  theories: [
    {
      id: 1,
      title: "Приветствие",
      description: "Основные фразы...",
      examples: [...]
    }
  ],
  questions: [
    {
      id: 1,
      type: "translation",
      words: [...],
      correct_answer: "привет"
    }
  ],
  pre_requisite_ids: [],
  level: 1
};
```

**💡 Смысл метода**: Получение полной структуры модуля со всем контентом для редактирования и предварительного просмотра.

#### 7.3 Создание модуля
```typescript
// POST /v1/module
interface CreateModuleData {
  name: string;
  theory_ids: number[];
  question_ids: number[];
  pre_requisite_ids: number[];
  level: number;
}

const createModule = async (moduleData: CreateModuleData) => {
  const response = await apiClient.post('/module', moduleData);
  return response.data.module;
};

// Пример создания модуля
const addModule = async () => {
  const module = await createModule({
    name: "Семья и родственники",
    theory_ids: [3, 4],
    question_ids: [10, 11, 12, 13],
    pre_requisite_ids: [1], // требует прохождения модуля 1
    level: 2
  });
  console.log('Создан модуль:', module);
};
```

**💡 Смысл метода**: Создание новых учебных модулей с определением последовательности обучения и зависимостей.

#### 7.4 Обновление модуля
```typescript
// PUT /v1/module
const updateModule = async (moduleId: number, moduleData: Partial<CreateModuleData>) => {
  const response = await apiClient.put('/module', {
    id: moduleId,
    ...moduleData
  });
  return response.data.module;
};
```

**💡 Смысл метода**: Редактирование структуры модулей для оптимизации процесса обучения.

#### 7.5 Удаление модуля
```typescript
// DELETE /v1/module?id={id}
const deleteModule = async (moduleId: number) => {
  const response = await apiClient.delete(`/module?id=${moduleId}`);
  return response.data;
};
```

**💡 Смысл метода**: Удаление неактуальных модулей с проверкой зависимостей и прогресса пользователей.

### 8. Управление прогрессом пользователей

#### 8.1 Получение прогресса пользователя по модулям
```typescript
// GET /v1/module-user-progress/{id}
const getUserProgress = async (userId: number) => {
  const response = await apiClient.get(`/module-user-progress/${userId}`);
  return response.data;
};

// Пример ответа
const progressExample = {
  user_id: 1,
  passed_modules: [
    {
      module_id: 1,
      module_name: "Базовые приветствия",
      completed_at: "2024-01-01T15:30:00Z",
      best_time: "00:03:45",
      attempts: 2,
      mistakes: 3
    }
  ],
  total_modules: 10,
  completion_percentage: 10.0
};
```

**💡 Смысл метода**: Анализ прогресса конкретного пользователя для оценки эффективности обучения и выявления проблемных областей.

#### 8.2 Получение серии дней обучения
```typescript
// GET /v1/progress/streak/{id}
const getUserStreak = async (userId: number) => {
  const response = await apiClient.get(`/progress/streak/${userId}`);
  return response.data;
};

// Пример ответа
const streakExample = {
  user_id: 1,
  current_streak: 7,
  max_streak: 15,
  last_activity: "2024-01-01T12:00:00Z"
};
```

**💡 Смысл метода**: Мониторинг регулярности обучения пользователей для анализа вовлеченности и мотивации.

### 9. Управление достижениями

#### 9.1 Получение достижений пользователя
```typescript
// GET /v1/achievements/{id}
const getUserAchievements = async (userId: number) => {
  const response = await apiClient.get(`/achievements/${userId}`);
  return response.data;
};

// Пример ответа
const achievementsExample = {
  user_id: 1,
  achievements: [
    {
      achievement_id: 1,
      user_id: 1,
      progress: 100,
      achieved: true,
      achievement: {
        id: 1,
        name: "Первые шаги",
        description: "Завершите первый модуль",
        type: "module_completion",
        target: 1
      }
    }
  ],
  total_achievements: 15,
  unlocked_count: 3
};
```

**💡 Смысл метода**: Просмотр достижений пользователя для анализа мотивации и прогресса в геймификации.

#### 9.2 Создание нового достижения
```typescript
// POST /v1/achievements
interface CreateAchievementData {
  name: string;
  description: string;
  type: string;
  target: number;
}

const createAchievement = async (achievementData: CreateAchievementData) => {
  const response = await apiClient.post('/achievements', achievementData);
  return response.data.achievement;
};

// Пример создания достижения
const addAchievement = async () => {
  const achievement = await createAchievement({
    name: "Марафонец",
    description: "Занимайтесь 30 дней подряд",
    type: "streak",
    target: 30
  });
  console.log('Создано достижение:', achievement);
};
```

**💡 Смысл метода**: Создание новых достижений для мотивации пользователей и геймификации процесса обучения.

### 10. Управление файлами

#### 10.1 Загрузка аудио файла
```typescript
// POST /v1/files/upload/audio
const uploadAudio = async (audioFile: File) => {
  const formData = new FormData();
  formData.append('audio', audioFile);

  const response = await apiClient.post('/files/upload/audio', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

// Пример использования
const handleAudioUpload = async (file: File) => {
  try {
    const result = await uploadAudio(file);
    console.log('Аудио загружено:', result.file_url);
    return result.file_url;
  } catch (error) {
    console.error('Ошибка загрузки:', error);
  }
};
```

**💡 Смысл метода**: Загрузка аудио файлов произношения для слов и предложений, обеспечивая мультимедийное обучение.

#### 10.2 Загрузка изображения
```typescript
// POST /v1/files/upload/image
const uploadImage = async (imageFile: File) => {
  const formData = new FormData();
  formData.append('image', imageFile);

  const response = await apiClient.post('/files/upload/image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};
```

**💡 Смысл метода**: Загрузка изображений для визуального сопровождения вопросов и теоретических материалов.

#### 10.3 Множественная загрузка файлов
```typescript
// POST /v1/files/upload/multiple
const uploadMultipleFiles = async (files: File[]) => {
  const formData = new FormData();
  files.forEach(file => {
    formData.append('files', file);
  });

  const response = await apiClient.post('/files/upload/multiple', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

// Пример ответа
const multipleUploadExample = {
  message: "Files processed",
  uploaded_files: [
    {
      file_name: "uuid-audio.mp3",
      file_url: "http://minio:9000/klingo-audio/uuid-audio.mp3",
      file_type: "audio"
    }
  ],
  errors: [],
  total_uploaded: 1,
  total_errors: 0
};
```

**💡 Смысл метода**: Массовая загрузка файлов для эффективного добавления большого количества мультимедиа контента.

#### 10.4 Получение списка файлов
```typescript
// GET /v1/files/list
const getFilesList = async (bucket: string, prefix?: string) => {
  const params = new URLSearchParams({ bucket });
  if (prefix) params.append('prefix', prefix);

  const response = await apiClient.get(`/files/list?${params}`);
  return response.data;
};

// Пример использования
const loadAudioFiles = async () => {
  const audioFiles = await getFilesList('klingo-audio');
  console.log('Аудио файлы:', audioFiles.files);
};

const loadImages = async () => {
  const images = await getFilesList('klingo-images');
  console.log('Изображения:', images.files);
};
```

**💡 Смысл метода**: Просмотр всех загруженных файлов для управления медиа-библиотекой и организации контента.

#### 10.5 Удаление файла
```typescript
// DELETE /v1/files/delete
const deleteFile = async (fileUrl: string) => {
  const response = await apiClient.delete(`/files/delete?url=${encodeURIComponent(fileUrl)}`);
  return response.data;
};

// Пример использования
const removeFile = async (fileUrl: string) => {
  try {
    await deleteFile(fileUrl);
    console.log('Файл удален:', fileUrl);
  } catch (error) {
    console.error('Ошибка удаления:', error);
  }
};
```

**💡 Смысл метода**: Удаление неиспользуемых или ошибочных файлов для оптимизации хранилища.

### 11. Системные методы

#### 11.1 Проверка состояния системы
```typescript
// GET /v1/healthcheck
const getSystemHealth = async () => {
  const response = await apiClient.get('/healthcheck');
  return response.data;
};

// Пример ответа
const healthExample = {
  status: "available",
  system_info: {
    environment: "production",
    version: "1.0.0"
  }
};
```

**💡 Смысл метода**: Мониторинг работоспособности системы для обеспечения стабильной работы платформы.

#### 11.2 Получение метрик системы
```typescript
// GET /debug/vars
const getSystemMetrics = async () => {
  const response = await apiClient.get('/debug/vars');
  return response.data;
};

// Пример ответа
const metricsExample = {
  total_requests_received: 1000,
  total_responses_sent: 1000,
  total_processing_time_μs: 50000000,
  total_responses_sent_by_status: {
    "200": 800,
    "400": 50,
    "401": 30,
    "404": 20,
    "500": 10
  }
};
```

**💡 Смысл метода**: Анализ производительности системы и выявление узких мест для оптимизации.

## 📋 Дополнительные API методы (требуют реализации)

### Статистика и аналитика

#### Общая статистика
```typescript
// GET /v1/admin/stats/overview
const getOverviewStats = async () => {
  const response = await apiClient.get('/admin/stats/overview');
  return response.data;
};

// Ожидаемый ответ
const overviewStatsExample = {
  total_users: 1250,
  active_users_today: 89,
  active_users_week: 456,
  total_modules: 15,
  total_questions: 450,
  total_words: 2300,
  avg_completion_rate: 67.5,
  popular_modules: [
    { id: 1, name: "Базовые приветствия", users_count: 234 },
    { id: 2, name: "Семья", users_count: 189 }
  ]
};
```

**💡 Смысл метода**: Получение ключевых показателей платформы для принятия управленческих решений.

#### Статистика по модулям
```typescript
// GET /v1/admin/stats/modules
const getModulesStats = async () => {
  const response = await apiClient.get('/admin/stats/modules');
  return response.data;
};

// Ожидаемый ответ
const moduleStatsExample = {
  modules: [
    {
      id: 1,
      name: "Базовые приветствия",
      total_attempts: 456,
      completion_rate: 78.5,
      average_time: "00:04:32",
      difficulty_rating: 2.3,
      most_difficult_questions: [
        { id: 5, error_rate: 45.2 },
        { id: 8, error_rate: 38.7 }
      ]
    }
  ]
};
```

**💡 Смысл метода**: Анализ эффективности каждого модуля для улучшения образовательного контента.

### Управление настройками

#### Получение настроек системы
```typescript
// GET /v1/admin/settings
const getSystemSettings = async () => {
  const response = await apiClient.get('/admin/settings');
  return response.data;
};

// Ожидаемый ответ
const settingsExample = {
  rate_limiting: {
    requests_per_second: 2,
    burst_limit: 4
  },
  file_limits: {
    max_audio_size_mb: 10,
    max_image_size_mb: 5,
    allowed_audio_formats: ["mp3", "wav", "ogg", "m4a"],
    allowed_image_formats: ["jpg", "jpeg", "png", "gif", "webp"]
  },
  notifications: {
    email_enabled: true,
    push_enabled: false
  }
};
```

**💡 Смысл метода**: Управление конфигурацией системы для настройки поведения платформы.

#### Обновление настроек
```typescript
// PUT /v1/admin/settings
const updateSystemSettings = async (settings: Partial<SystemSettings>) => {
  const response = await apiClient.put('/admin/settings', settings);
  return response.data;
};
```

**💡 Смысл метода**: Изменение параметров системы для оптимизации работы и безопасности.

## 🔧 Технические детали реализации

### TypeScript типы

```typescript
// Основные типы данных
interface User {
  id: number;
  name: string;
  surname: string;
  email: string;
  image_url: string;
  activated: boolean;
  created_at: string;
  last_activity?: string;
  status: 'active' | 'blocked';
}

interface Word {
  id: number;
  kaz_plaintext: string;
  rus_plaintext: string;
  audio_url: string;
}

interface Sentence {
  id: number;
  kaz_plaintext: string;
  rus_plaintext: string;
  audio_url: string;
}

interface Question {
  id: number;
  type: string;
  words: Word[];
  correct_answer: string;
  image_url: string;
}

interface Theory {
  id: number;
  title: string;
  description: string;
  module_id: number;
  tags: string[];
  example_ids: number[];
  examples: Sentence[];
  created_at: string;
}

interface Module {
  id: number;
  name: string;
  theory_ids: number[];
  question_ids: number[];
  questions: Question[];
  theories: Theory[];
  pre_requisite_ids: number[];
  level: number;
  created_at: string;
}

interface Achievement {
  id: number;
  name: string;
  description: string;
  type: string;
  target: number;
  created_at: string;
  updated_at: string;
}

interface UserProgress {
  id: number;
  user_id: number;
  module_id: number;
  mistaken_question_ids: number[];
  time: string;
  try_count: number;
  created_at: string;
  updated_at: string;
}

// API Response типы
interface ApiResponse<T> {
  data: T;
  message?: string;
  error?: string;
}

interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Фильтры и параметры
interface UserFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'active' | 'blocked';
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'name' | 'email' | 'created_at' | 'last_activity';
  sortOrder?: 'asc' | 'desc';
}

interface ContentFilters {
  page?: number;
  limit?: number;
  search?: string;
  tags?: string[];
  level?: number;
  module_id?: number;
}
```

### Обработка ошибок

```typescript
// Централизованная обработка ошибок
class ApiError extends Error {
  constructor(
    public status: number,
    public message: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Interceptor для обработки ошибок
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // Перенаправление на страницу входа
          localStorage.removeItem('accessToken');
          window.location.href = '/login';
          break;
        case 403:
          throw new ApiError(403, 'Недостаточно прав доступа');
        case 404:
          throw new ApiError(404, 'Ресурс не найден');
        case 422:
          throw new ApiError(422, 'Ошибка валидации', data.error);
        case 429:
          throw new ApiError(429, 'Превышен лимит запросов');
        case 500:
          throw new ApiError(500, 'Внутренняя ошибка сервера');
        default:
          throw new ApiError(status, data.error || 'Неизвестная ошибка');
      }
    }

    throw new ApiError(0, 'Ошибка сети');
  }
);
```

### Состояние приложения (Redux/Zustand)

```typescript
// Store для управления состоянием
interface AppState {
  auth: {
    user: User | null;
    isAuthenticated: boolean;
    loading: boolean;
  };
  users: {
    list: User[];
    total: number;
    loading: boolean;
    filters: UserFilters;
  };
  content: {
    words: Word[];
    sentences: Sentence[];
    questions: Question[];
    theories: Theory[];
    modules: Module[];
    loading: boolean;
  };
  files: {
    audioFiles: string[];
    imageFiles: string[];
    uploading: boolean;
  };
  stats: {
    overview: OverviewStats | null;
    moduleStats: ModuleStats[];
    loading: boolean;
  };
}

// Actions для управления состоянием
interface AppActions {
  // Auth actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;

  // Users actions
  fetchUsers: (filters?: UserFilters) => Promise<void>;
  updateUserStatus: (userId: number, status: string) => Promise<void>;

  // Content actions
  fetchWords: () => Promise<void>;
  createWord: (word: CreateWordData) => Promise<void>;
  updateWord: (id: number, word: Partial<CreateWordData>) => Promise<void>;
  deleteWord: (id: number) => Promise<void>;

  // Files actions
  uploadFile: (file: File, type: 'audio' | 'image') => Promise<string>;
  deleteFile: (url: string) => Promise<void>;

  // Stats actions
  fetchOverviewStats: () => Promise<void>;
  fetchModuleStats: () => Promise<void>;
}
```

### Компоненты React

```typescript
// Пример компонента для управления пользователями
const UsersManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<UserFilters>({
    page: 1,
    limit: 20
  });

  const loadUsers = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getUsers(filters);
      setUsers(response.users);
    } catch (error) {
      console.error('Ошибка загрузки пользователей:', error);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  const handleStatusChange = async (userId: number, status: string) => {
    try {
      await updateUserStatus(userId, status);
      await loadUsers(); // Перезагрузка списка
    } catch (error) {
      console.error('Ошибка изменения статуса:', error);
    }
  };

  return (
    <div>
      <UserFilters filters={filters} onChange={setFilters} />
      <UserTable
        users={users}
        loading={loading}
        onStatusChange={handleStatusChange}
      />
    </div>
  );
};
```

## 📅 План реализации

### Этап 1: Базовая инфраструктура (1-2 недели)
1. **Настройка проекта**
   - Создание React приложения с TypeScript
   - Настройка роутинга и базовой структуры
   - Подключение UI библиотеки (Material-UI/Ant Design)

2. **Аутентификация**
   - Страница входа
   - Управление JWT токенами
   - Защищенные маршруты

3. **Базовый layout**
   - Навигационное меню
   - Header с информацией о пользователе
   - Sidebar с разделами админ панели

### Этап 2: Управление пользователями (1-2 недели)
1. **Список пользователей**
   - Таблица с пагинацией
   - Поиск и фильтрация
   - Сортировка

2. **Детальная информация**
   - Профиль пользователя
   - Статистика прогресса
   - История активности

3. **Действия с пользователями**
   - Блокировка/разблокировка
   - Редактирование профиля
   - Сброс пароля

### Этап 3: Управление контентом (2-3 недели)
1. **Словарь (Words)**
   - CRUD операции
   - Массовый импорт
   - Поиск и фильтрация

2. **Предложения (Sentences)**
   - Управление примерами
   - Привязка к теориям
   - Аудио файлы

3. **Вопросы (Questions)**
   - Конструктор вопросов
   - Привязка слов
   - Предварительный просмотр

4. **Теории (Theories)**
   - Rich text редактор
   - Управление примерами
   - Теги и категории

5. **Модули (Modules)**
   - Конструктор модулей
   - Drag & drop интерфейс
   - Управление зависимостями

### Этап 4: Файловый менеджер (1-2 недели)
1. **Браузер файлов**
   - Просмотр buckets
   - Навигация по папкам
   - Предварительный просмотр

2. **Загрузка файлов**
   - Drag & drop интерфейс
   - Прогресс загрузки
   - Валидация типов и размеров

3. **Управление файлами**
   - Переименование
   - Удаление
   - Организация в папки

### Этап 5: Аналитика и статистика (1-2 недели)
1. **Dashboard**
   - Ключевые метрики
   - Графики активности
   - Быстрые действия

2. **Детальная аналитика**
   - Статистика пользователей
   - Анализ контента
   - Отчеты по модулям

3. **Визуализация данных**
   - Интерактивные графики
   - Экспорт отчетов
   - Настраиваемые дашборды

### Этап 6: Система достижений (1 неделя)
1. **Управление достижениями**
   - CRUD операции
   - Конструктор условий
   - Иконки и оформление

2. **Мониторинг прогресса**
   - Статистика получения
   - Анализ эффективности
   - Настройка сложности

### Этап 7: Системное администрирование (1 неделя)
1. **Настройки системы**
   - Конфигурация параметров
   - Управление лимитами
   - Интеграции

2. **Мониторинг**
   - Логи системы
   - Метрики производительности
   - Алерты и уведомления

### Этап 8: Тестирование и оптимизация (1-2 недели)
1. **Тестирование**
   - Unit тесты компонентов
   - Integration тесты API
   - E2E тесты пользовательских сценариев

2. **Оптимизация**
   - Производительность
   - Доступность
   - SEO (если необходимо)

3. **Документация**
   - Руководство пользователя
   - Техническая документация
   - API документация

## 🚀 Развертывание и поддержка

### Развертывание
1. **Сборка production версии**
2. **Настройка веб-сервера (Nginx)**
3. **SSL сертификаты**
4. **Мониторинг и логирование**

### Поддержка
1. **Обновления и патчи**
2. **Резервное копирование**
3. **Мониторинг производительности**
4. **Техническая поддержка пользователей**

**Общее время реализации: 8-12 недель**

Данное техническое задание предоставляет полную картину для разработки админ панели с детальными примерами API запросов и описанием функциональности каждого метода.
