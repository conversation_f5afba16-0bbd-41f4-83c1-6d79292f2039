package main

import (
	"errors"
	"net/http"
	"strconv"
	"time"

	"github.com/olzzhas/kazakh-lingo/internal/data"
	"github.com/olzzhas/kazakh-lingo/internal/validator"
)

// getUsersHandler получение списка пользователей с фильтрацией
func (app *application) getUsersHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем параметры фильтрации из query string
	filters := data.UserFilters{}

	// Парсим параметры
	if pageStr := r.URL.Query().Get("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filters.Page = page
		}
	}

	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 100 {
			filters.Limit = limit
		}
	}

	filters.Search = r.URL.Query().Get("search")
	filters.Status = r.URL.Query().Get("status")
	filters.DateFrom = r.URL.Query().Get("dateFrom")
	filters.DateTo = r.URL.Query().Get("dateTo")

	// Получаем список пользователей
	result, err := app.models.Users.GetAll(filters)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	// Возвращаем результат
	err = app.writeJSON(w, http.StatusOK, envelope{"data": result}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// getUserDetailsHandler получение детальной информации о пользователе
func (app *application) getUserDetailsHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем ID пользователя из URL
	userID, err := app.readIDParam(r)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Получаем детальную информацию о пользователе
	userDetails, err := app.models.Users.GetUserDetails(int(userID))
	if err != nil {
		switch {
		case errors.Is(err, data.ErrRecordNotFound):
			app.notFoundResponse(w, r)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Возвращаем результат
	err = app.writeJSON(w, http.StatusOK, envelope{"data": userDetails}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// updateUserStatusHandler обновление статуса пользователя (блокировка/разблокировка)
func (app *application) updateUserStatusHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем ID пользователя из URL
	userID, err := app.readIDParam(r)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Парсим тело запроса
	var input struct {
		Status string `json:"status"` // "active" или "blocked"
		Reason string `json:"reason"` // причина изменения статуса
	}

	err = app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Валидируем статус
	var activated bool
	switch input.Status {
	case "active":
		activated = true
	case "blocked":
		activated = false
	default:
		app.badRequestResponse(w, r, errors.New("status must be 'active' or 'blocked'"))
		return
	}

	// Обновляем статус пользователя
	err = app.models.Users.UpdateUserStatus(int(userID), activated, input.Reason)
	if err != nil {
		switch {
		case errors.Is(err, data.ErrRecordNotFound):
			app.notFoundResponse(w, r)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Возвращаем успешный ответ
	response := envelope{
		"message": "User status updated successfully",
		"user_id": userID,
		"status":  input.Status,
	}

	err = app.writeJSON(w, http.StatusOK, response, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// getUserProgressHandler получение прогресса пользователя по модулям
func (app *application) getUserProgressHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем ID пользователя из URL
	userID, err := app.readIDParam(r)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Получаем прогресс пользователя (используем существующий метод)
	progress, err := app.models.Modules.GetUserPassedModules(userID)
	if err != nil {
		switch {
		case errors.Is(err, data.ErrRecordNotFound):
			app.notFoundResponse(w, r)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Возвращаем результат
	err = app.writeJSON(w, http.StatusOK, envelope{"data": progress}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// adminMiddleware проверяет права администратора
func (app *application) adminMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Получаем пользователя из контекста
		user := app.contextGetUser(r)
		if user == nil {
			app.authenticationRequiredResponse(w, r)
			return
		}

		// Проверяем права администратора
		if !user.IsAdmin() {
			app.notPermittedResponse(w, r)
			return
		}

		next(w, r)
	}
}

// createAdminHandler создает нового администратора (только для существующих админов)
func (app *application) createAdminHandler(w http.ResponseWriter, r *http.Request) {
	var input struct {
		Name     string `json:"name"`
		Surname  string `json:"surname"`
		Email    string `json:"email"`
		Password string `json:"password"`
		ImageUrl string `json:"imageUrl"`
	}

	err := app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	user := &data.User{
		Name:      input.Name,
		Surname:   input.Surname,
		Email:     input.Email,
		Role:      data.RoleAdmin, // Устанавливаем роль администратора
		ImageUrl:  input.ImageUrl,
		Activated: true,
	}

	err = user.Password.Set(input.Password)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	v := validator.New()

	if data.ValidateUser(v, user); !v.Valid() {
		app.failedValidationResponse(w, r, v.Errors)
		return
	}

	err = app.models.Users.Insert(user)
	if err != nil {
		switch {
		case errors.Is(err, data.ErrDuplicateEmail):
			v.AddError("email", "a user with this email address already exists")
			app.failedValidationResponse(w, r, v.Errors)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Логируем создание администратора
	logData := map[string]any{
		"admin_id":    user.ID,
		"admin_email": user.Email,
		"created_by":  app.contextGetUser(r).Email,
		"time":        time.Now().Format(time.RFC3339),
	}
	app.logger.PrintInfo("Admin user created", logData, "admin")

	response := map[string]interface{}{
		"user": user,
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"admin": response}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

// updateUserRoleHandler изменяет роль пользователя
func (app *application) updateUserRoleHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем ID пользователя из URL
	userID, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || userID < 1 {
		app.badRequestResponse(w, r, errors.New("invalid user ID"))
		return
	}

	var input struct {
		Role   string `json:"role"`
		Reason string `json:"reason"`
	}

	err = app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Валидация роли
	if !data.IsValidRole(input.Role) {
		app.badRequestResponse(w, r, errors.New("invalid role"))
		return
	}

	// Получаем пользователя
	user, err := app.models.Users.Get(userID)
	if err != nil {
		switch {
		case errors.Is(err, data.ErrRecordNotFound):
			app.notFoundResponse(w, r)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Проверяем, не пытается ли админ изменить свою собственную роль
	currentUser := app.contextGetUser(r)
	if user.ID == currentUser.ID {
		app.badRequestResponse(w, r, errors.New("cannot change your own role"))
		return
	}

	// Сохраняем старую роль для логирования
	oldRole := user.Role

	// Обновляем роль
	err = app.models.Users.UpdateUserRole(userID, input.Role, input.Reason)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	// Логируем изменение роли
	logData := map[string]any{
		"user_id":     userID,
		"user_email":  user.Email,
		"old_role":    oldRole,
		"new_role":    input.Role,
		"reason":      input.Reason,
		"changed_by":  currentUser.Email,
		"time":        time.Now().Format(time.RFC3339),
	}
	app.logger.PrintInfo("User role changed", logData, "admin")

	// Получаем обновленного пользователя
	updatedUser, err := app.models.Users.Get(userID)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"user": updatedUser}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}
