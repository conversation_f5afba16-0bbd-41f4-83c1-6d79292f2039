package main

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/olzzhas/kazakh-lingo/internal/data"
)

// getUsersHandler получение списка пользователей с фильтрацией
func (app *application) getUsersHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем параметры фильтрации из query string
	filters := data.UserFilters{}

	// Парсим параметры
	if pageStr := r.URL.Query().Get("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filters.Page = page
		}
	}

	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 100 {
			filters.Limit = limit
		}
	}

	filters.Search = r.URL.Query().Get("search")
	filters.Status = r.URL.Query().Get("status")
	filters.DateFrom = r.URL.Query().Get("dateFrom")
	filters.DateTo = r.URL.Query().Get("dateTo")

	// Получаем список пользователей
	result, err := app.models.Users.GetAll(filters)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	// Возвращаем результат
	err = app.writeJSON(w, http.StatusOK, envelope{"data": result}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// getUserDetailsHandler получение детальной информации о пользователе
func (app *application) getUserDetailsHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем ID пользователя из URL
	userID, err := app.readIDParam(r)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Получаем детальную информацию о пользователе
	userDetails, err := app.models.Users.GetUserDetails(int(userID))
	if err != nil {
		switch {
		case errors.Is(err, data.ErrRecordNotFound):
			app.notFoundResponse(w, r)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Возвращаем результат
	err = app.writeJSON(w, http.StatusOK, envelope{"data": userDetails}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// updateUserStatusHandler обновление статуса пользователя (блокировка/разблокировка)
func (app *application) updateUserStatusHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем ID пользователя из URL
	userID, err := app.readIDParam(r)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Парсим тело запроса
	var input struct {
		Status string `json:"status"` // "active" или "blocked"
		Reason string `json:"reason"` // причина изменения статуса
	}

	err = app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Валидируем статус
	var activated bool
	switch input.Status {
	case "active":
		activated = true
	case "blocked":
		activated = false
	default:
		app.badRequestResponse(w, r, errors.New("status must be 'active' or 'blocked'"))
		return
	}

	// Обновляем статус пользователя
	err = app.models.Users.UpdateUserStatus(int(userID), activated, input.Reason)
	if err != nil {
		switch {
		case errors.Is(err, data.ErrRecordNotFound):
			app.notFoundResponse(w, r)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Возвращаем успешный ответ
	response := envelope{
		"message": "User status updated successfully",
		"user_id": userID,
		"status":  input.Status,
	}

	err = app.writeJSON(w, http.StatusOK, response, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// getUserProgressHandler получение прогресса пользователя по модулям
func (app *application) getUserProgressHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем ID пользователя из URL
	userID, err := app.readIDParam(r)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Получаем прогресс пользователя (используем существующий метод)
	progress, err := app.models.Modules.GetUserPassedModules(userID)
	if err != nil {
		switch {
		case errors.Is(err, data.ErrRecordNotFound):
			app.notFoundResponse(w, r)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Возвращаем результат
	err = app.writeJSON(w, http.StatusOK, envelope{"data": progress}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// adminMiddleware проверяет права администратора
func (app *application) adminMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Получаем пользователя из контекста
		user := app.contextGetUser(r)
		if user == nil {
			app.authenticationRequiredResponse(w, r)
			return
		}

		// Проверяем права администратора
		// TODO: Добавить поле role в структуру User и проверить его
		// Пока что проверяем по email (временное решение)
		if user.Email != "<EMAIL>" {
			app.notPermittedResponse(w, r)
			return
		}

		next(w, r)
	}
}

// notPermittedResponse отправляет ответ о недостатке прав
func (app *application) notPermittedResponse(w http.ResponseWriter, r *http.Request) {
	message := "your user account doesn't have the necessary permissions to access this resource"
	app.errorResponse(w, r, http.StatusForbidden, message)
}
